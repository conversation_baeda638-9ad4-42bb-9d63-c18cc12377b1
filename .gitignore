# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

/public/assets
.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key

/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

# Ignore .env files (environment variables)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.sqlite3
*.sqlite3-*
/db/*.sqlite3
/db/*.sqlite3-journal
/db/*.sqlite3-*

# Ignore uploaded files
/public/uploads
/public/system

# Ignore precompiled assets
/public/assets
/public/packs
/public/packs-test

# Ignore Spring files
/spring/*.pid

# Ignore coverage reports
/coverage/

# Ignore IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore RubyMine files
.idea

# Ignore vim files
*.swp
*.swo

# Ignore Emacs files
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Ignore Sublime Text files
*.sublime-project
*.sublime-workspace

# Ignore TextMate files
*.tmproj
*.tmproject
tmtags

# Ignore CTags files
tags
TAGS

# Ignore backup files
*.bak
*.backup
*.tmp

# Ignore log files
*.log

# Ignore temporary files
tmp/
temp/

# Ignore node modules and npm files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore webpack compiled files
/public/webpack/
/public/packs/
/public/packs-test/

# Ignore carrierwave uploads
/public/uploads/

# Ignore bootsnap cache
/tmp/cache/bootsnap*

# Ignore Spring files
/spring/*.pid

# Ignore Guard files
/Guardfile.lock

# Ignore simplecov coverage
/coverage/

# Ignore RSpec files
/spec/examples.txt

# Ignore Capistrano files
/config/deploy/*
/log/capistrano.log

# Ignore secrets
/config/secrets.yml
/config/database.yml.example

# Ignore local configuration files
/config/application.yml
/config/local_env.yml

# Ignore Redis dump file
dump.rdb

# Ignore Elasticsearch data
/elasticsearch/

# Ignore Solr data
/solr/

# Ignore pow files
.powrc
.powder

# Ignore rbenv files
.ruby-version
.ruby-gemset

# Ignore RVM files
.rvmrc

# Ignore Bundler files
vendor/bundle/
vendor/cache/
.bundle/

# Ignore Sass cache
.sass-cache/

# Ignore Compass files
.compass/

# Ignore Middleman files
.middleman/

# Ignore Nanoc files
/output/
/tmp/nanoc/

# Ignore Jekyll files
_site/
.jekyll-metadata

# Ignore Octopress files
.preview-mode

# Ignore Heroku files
.slugignore

# Ignore local development files
.env.development
.env.test
.env.production

# Ignore Docker files (if using Docker)
docker-compose.override.yml

# Ignore Vagrant files
.vagrant/

# Ignore Terraform files
*.tfstate
*.tfstate.*
.terraform/

# Ignore local SSL certificates
*.pem
*.key
*.crt

# Ignore local configuration
.local
