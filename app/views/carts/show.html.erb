<div class="container">
  <h1 class="title">Your Shopping Cart</h1>

  <% if @cart_items.any? %>
    <div class="box">
      <% @cart_items.each do |item| %>
        <div class="columns is-vcentered" style="border-bottom: 1px solid #eee; padding: 1rem 0;">
          <div class="column is-2">
            <% if item.product.image.present? %>
              <%= image_tag item.product.image.url, class: "image is-64x64", style: "object-fit: cover;" %>
            <% else %>
              <div class="image is-64x64" style="background-color: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                <span class="has-text-grey">No Image</span>
              </div>
            <% end %>
          </div>

          <div class="column is-4">
            <h4 class="title is-5"><%= item.product.title %></h4>
            <p class="subtitle is-6"><%= item.product.brand %> - <%= item.product.model %></p>
          </div>

          <div class="column is-2">
            <%= form_with url: update_cart_quantity_path(item), method: :patch, local: true, class: "field has-addons" do |form| %>
              <div class="control">
                <%= form.number_field :quantity, value: item.quantity, min: 1, max: 99, class: "input is-small", style: "width: 60px;" %>
              </div>
              <div class="control">
                <%= form.submit "Update", class: "button is-small is-info" %>
              </div>
            <% end %>
          </div>

          <div class="column is-2">
            <span class="has-text-weight-bold">$<%= item.product.price %></span>
          </div>

          <div class="column is-2">
            <span class="has-text-weight-bold">$<%= item.total_price %></span>
          </div>

          <div class="column is-1">
            <%= link_to remove_from_cart_path(item), method: :delete,
                        class: "button is-small is-danger",
                        data: { confirm: "Are you sure you want to remove this item?", turbo_method: :delete } do %>
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
            <% end %>
          </div>
        </div>
      <% end %>

      <div class="has-text-right" style="margin-top: 2rem; padding-top: 1rem; border-top: 2px solid #3273dc;">
        <h3 class="title is-4">Total: $<%= @cart.total_price %></h3>
        <p class="subtitle is-6">(<%= @cart.total_items %> items)</p>
      </div>

      <div class="buttons is-right" style="margin-top: 2rem;">
        <%= link_to "Continue Shopping", shop_path, class: "button is-light" %>
        <%= link_to "Empty Cart", empty_cart_cart_path(@cart),
                    method: :delete,
                    class: "button is-warning",
                    data: { confirm: "Are you sure you want to empty your cart?", turbo_method: :delete } %>
        <button class="button is-primary is-large">
          <span class="icon">
            <i class="fas fa-credit-card"></i>
          </span>
          <span>Checkout</span>
        </button>
      </div>
    </div>
  <% else %>
    <div class="box has-text-centered">
      <h2 class="title is-4">Your cart is empty</h2>
      <p class="subtitle">Start shopping to add items to your cart!</p>
      <%= link_to "Start Shopping", shop_path, class: "button is-primary" %>
    </div>
  <% end %>
</div>
