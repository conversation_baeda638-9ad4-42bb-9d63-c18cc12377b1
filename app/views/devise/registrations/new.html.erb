<div class="section">
  <div class="container">
  <div class="columns is-centered">

    <div class="column is-4">

    <h2 class="title is-2">Sign Up</h2>
<% if resource.errors.any? %>
  <div class="notification is-danger">
    <ul>
      <% resource.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
    </ul>
  </div>
<% end %>

    <%= simple_form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
    <%= f.error_notification %>

    <div class="field">
      <div class="control">
      <%= f.input :name, required: true, autofocus: true, input_html: { class:"input" }, wrapper: false, label_html: { class:"label" } %>
      </div>
    </div>

    <div class="field">
      <div class="control">
      <%= f.input :email, required: true, input_html: { class:"input" }, wrapper: false, label_html: { class:"label" } %>
      </div>
    </div>

    <div class="field">
      <div class="control">
        <%= f.input :password, required: true, input_html: { class:"input" }, wrapper: false, label_html: { class:"label" }, hint: ("#{@minimum_password_length} characters minimum" if @minimum_password_length) %>
      </div>
    </div>

    <div class="field">
      <div class="control">
        <%= f.input :password_confirmation, required: true, input_html: { class: "input" }, wrapper: false, label_html: { class: "label" } %>
      </div>
    </div>

    <div class="field">
      <div class="control">
        <%= f.button :submit, "Sign up", class:"button is-warning is-medium" %>
      </div>
    </div>

    <% end %>
      <br />
      <%= render "devise/shared/links" %>
    </div>
    </div>
  </div>
</div>