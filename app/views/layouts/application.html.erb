<!DOCTYPE html>
<html>
  <head>
    <title>Shop</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag 'application', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_include_tag 'application', 'data-turbolinks-track': 'reload' %>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  </head>

   <body class="<%= yield (:body_class) %>">
    <% flash.each do |key, value| %>
  <div class="flash-message <%= key %>"><%= value %></div>
<% end %>

    <% if flash[:notice] %>
      <div class="notification is-success global-notification">
        <p class="notice"><%= notice %></p>
      </div>
    <% end %>
    <% if flash[:alert] %>
    <div class="notification is-danger global-notification">
      <p class="alert"><%= alert %></p>
    </div>
    <% end %>
     <nav class="navbar is-warning" role="navigation" aria-label="main navigation">
      <div class="navbar-brand">
        <%= link_to (user_signed_in? ? shop_path : root_path), class:"navbar-item" do %>
          <h1 class="title is-5">Shop</h1>
        <% end  %>
      <div class="navbar-burger burger" data-target="navbar">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>

      <div id="navbar" class="navbar-menu">
        <div class="navbar-end">
          <div class="navbar-item">
            <div class="field is-grouped">

            <!-- Cart Icon -->
            <% if @cart %>
              <%= link_to cart_path(@cart), class: "navbar-item" do %>
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-shopping-cart"></i>
                  </span>
                  <span class="tag is-primary is-rounded">
                    <%= @cart.total_items rescue 0 %>
                  </span>
                </span>
              <% end %>
            <% else %>
              <span class="navbar-item">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-shopping-cart"></i>
                  </span>
                  <span class="tag is-primary is-rounded">0</span>
                </span>
              </span>
            <% end %>

            <% if user_signed_in? %>
              <%= link_to 'Sell', new_product_path, class: "navbar-item button is-dark" %>

              <div class="navbar-item has-dropdown is-hoverable">
                <%= link_to 'Account', edit_user_registration_path, class: "navbar-link" %>
                <div class="navbar-dropdown is-right">
                  <%= link_to current_user.name, edit_user_registration_path, class:"navbar-item" %>
                  <%= link_to "Log Out", destroy_user_session_path, method: :delete, class:"navbar-item", data: { turbo_method: :delete } %>
                </div>
              </div>
            <% else %>

            <%= link_to "Sign In", new_user_session_path, class:"navbar-item button is-warning" %>
            <%= link_to "Sign up", new_user_registration_path, class:"navbar-item button is-warning"%>

            <% end %>

            </div>
          </div>
        </div>
    </div>
  </nav>

  <%= yield(:header) %>
  
  <div class="container">

    <%= yield %>

  </div>

  </body>
</html>
