<!DOCTYPE html>
<html>
<head>
    <title>Shop - Interactive E-Commerce Test</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-index-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1.5rem; padding: 1rem; }
        @media (max-width: 768px) {
            .product-index-grid { grid-template-columns: 1fr; padding: 1rem; gap: 1rem; }
            .login-box { margin: 1rem; padding: 2rem; }
            .navbar-menu { display: block !important; }
            .container { padding: 0 1rem; }
        }
        .product { border: 1px solid #eee; border-radius: 8px; overflow: hidden; transition: transform 0.2s; }
        .product:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .product-thumb { position: relative; height: 200px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem; text-align: center; }
        .condition { position: absolute; top: 10px; right: 10px; }
        .pa3 { padding: 1rem; }
        .product-image { width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); }
        .ferrari { background: linear-gradient(45deg, #ff6b6b, #ee5a52); }
        .macbook { background: linear-gradient(45deg, #4ecdc4, #44a08d); }
        .watch { background: linear-gradient(45deg, #45b7d1, #3498db); }
        .chair { background: linear-gradient(45deg, #96ceb4, #85c1a3); }
        .phone { background: linear-gradient(45deg, #feca57, #ff9ff3); }
        .headphones { background: linear-gradient(45deg, #ff9ff3, #f093fb); }
        .is-focused { border-color: #3273dc !important; box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25) !important; }
        .product-detail-image { border-radius: 8px; overflow: hidden; }
        .product-detail-info { padding-left: 2rem; }
        .price-section { border: 2px solid #3273dc; border-radius: 8px; padding: 1rem; text-align: center; background: #f8f9fa; }
        .notification { position: fixed; top: 20px; right: 20px; z-index: 1000; min-width: 300px; }
        .fade-out { animation: fadeOut 3s forwards; }
        @keyframes fadeOut { 0% { opacity: 1; } 70% { opacity: 1; } 100% { opacity: 0; display: none; } }
        .cart-badge { animation: bounce 0.5s; }
        @keyframes bounce { 0%, 20%, 60%, 100% { transform: translateY(0); } 40% { transform: translateY(-10px); } 80% { transform: translateY(-5px); } }
        .login-container { min-height: 100vh; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .login-box { background: white; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); padding: 3rem; max-width: 400px; width: 100%; }
    </style>
</head>
<body>
    <!-- Login Screen (Initial) -->
    <div id="login-screen" class="login-container">
        <div class="login-box">
            <div class="has-text-centered" style="margin-bottom: 2rem;">
                <h1 class="title is-3">
                    <span class="icon-text">
                        <span class="icon has-text-primary">
                            <i class="fas fa-shopping-bag"></i>
                        </span>
                        <span>Shop</span>
                    </span>
                </h1>
                <p class="subtitle is-6">Welcome to our e-commerce platform</p>
            </div>

            <div class="field">
                <label class="label">Email</label>
                <div class="control has-icons-left">
                    <input class="input" type="email" placeholder="Enter your email" value="<EMAIL>">
                    <span class="icon is-small is-left">
                        <i class="fas fa-envelope"></i>
                    </span>
                </div>
            </div>

            <div class="field">
                <label class="label">Password</label>
                <div class="control has-icons-left">
                    <input class="input" type="password" placeholder="Enter your password" value="password">
                    <span class="icon is-small is-left">
                        <i class="fas fa-lock"></i>
                    </span>
                </div>
            </div>

            <button class="button is-primary is-fullwidth" onclick="login()">
                <span class="icon">
                    <i class="fas fa-sign-in-alt"></i>
                </span>
                <span>Sign In</span>
            </button>

            <div class="has-text-centered" style="margin-top: 1rem;">
                <p class="has-text-grey">Demo credentials are pre-filled. Click Sign In to test!</p>
            </div>
        </div>
    </div>

    <!-- Main Application (Hidden initially) -->
    <div id="main-app" style="display: none;">
        <!-- Navigation -->
        <nav class="navbar is-warning" role="navigation">
            <div class="navbar-brand">
                <a class="navbar-item" href="#" onclick="showShop()">
                    <h1 class="title is-5">Shop</h1>
                </a>
                <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" onclick="toggleNavbar()">
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                </a>
            </div>
            <div class="navbar-menu" id="navbar-menu">
                <div class="navbar-end">
                    <div class="navbar-item">
                        <!-- Cart Icon -->
                        <a class="navbar-item" href="#" onclick="showCart()">
                            <span class="icon-text">
                                <span class="icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </span>
                                <span class="tag is-primary is-rounded" id="cart-count">0</span>
                            </span>
                        </a>
                        <a class="navbar-item button is-dark" href="#" onclick="showSell()">Sell</a>
                        <div class="navbar-item has-dropdown" onclick="toggleAccountDropdown()">
                            <a class="navbar-link">Account</a>
                            <div class="navbar-dropdown is-right">
                                <a class="navbar-item" onclick="showProfile()">Demo User</a>
                                <a class="navbar-item" onclick="logout()">Log Out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Flash Messages Container -->
        <div id="flash-container"></div>

        <!-- Hero Section -->
        <section class="hero is-warning" id="hero-section">
            <div class="hero-body">
                <div class="container">
                    <h1 class="title">Browse new products</h1>
                    <p class="subtitle">Find amazing deals from our sellers</p>
                </div>
            </div>
        </section>

        <!-- Products Grid -->
        <div id="products-section" class="product-index-grid">
            <!-- Products will be loaded here -->
        </div>

        <!-- Cart Section (Hidden initially) -->
        <div id="cart-section" class="container" style="display: none; padding: 2rem;">
            <h1 class="title">Your Shopping Cart</h1>
            <div id="cart-items"></div>
            <div id="cart-total" class="has-text-right" style="margin-top: 2rem; padding-top: 1rem; border-top: 2px solid #3273dc;">
                <h3 class="title is-4">Total: $<span id="total-price">0.00</span></h3>
                <p class="subtitle is-6">(<span id="total-items">0</span> items)</p>
            </div>
            <div class="buttons is-right" style="margin-top: 2rem;">
                <button class="button is-light" onclick="showShop()">Continue Shopping</button>
                <button class="button is-warning" onclick="emptyCart()">Empty Cart</button>
                <button class="button is-primary is-large" onclick="showCheckout()">
                    <span class="icon">
                        <i class="fas fa-credit-card"></i>
                    </span>
                    <span>Checkout</span>
                </button>
            </div>
        </div>

        <!-- Profile Section (Hidden initially) -->
        <div id="profile-section" class="container" style="display: none; padding: 2rem;">
            <h1 class="title">My Profile</h1>
            <div class="columns">
                <div class="column is-8">
                    <div class="box">
                        <h2 class="subtitle">Account Information</h2>
                        <div class="field">
                            <label class="label">Name</label>
                            <div class="control">
                                <input class="input" id="profile-name" type="text" value="Demo User" readonly>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">Email</label>
                            <div class="control">
                                <input class="input" id="profile-email" type="email" value="<EMAIL>" readonly>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">Member Since</label>
                            <div class="control">
                                <input class="input" type="text" value="January 2024" readonly>
                            </div>
                        </div>
                        <div class="field is-grouped">
                            <div class="control">
                                <button class="button is-primary" onclick="toggleEditProfile()">
                                    <span id="edit-profile-text">Edit Profile</span>
                                </button>
                            </div>
                            <div class="control">
                                <button class="button is-light" onclick="showShop()">Back to Shop</button>
                            </div>
                        </div>
                    </div>

                    <div class="box">
                        <h2 class="subtitle">My Products</h2>
                        <div id="my-products-list">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="column is-4">
                    <div class="box">
                        <h2 class="subtitle">Statistics</h2>
                        <div class="content">
                            <p><strong>Products Listed:</strong> <span id="products-count">0</span></p>
                            <p><strong>Total Sales:</strong> $<span id="total-sales">0</span></p>
                            <p><strong>Items in Cart:</strong> <span id="profile-cart-count">0</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sell Product Section (Hidden initially) -->
        <div id="sell-section" class="container" style="display: none; padding: 2rem;">
            <h1 class="title">Sell a Product</h1>
            <div class="columns">
                <div class="column is-8">
                    <form id="product-form">
                        <div class="columns">
                            <div class="field column is-9">
                                <label class="label">Title</label>
                                <div class="control">
                                    <input class="input" type="text" id="product-title" placeholder="Product title" required>
                                </div>
                            </div>
                            <div class="field column">
                                <label class="label">Price</label>
                                <div class="control">
                                    <input class="input" type="number" id="product-price" placeholder="0.00" step="0.01" required>
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Model</label>
                            <div class="control">
                                <input class="input" type="text" id="product-model" placeholder="Product model" required>
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Description</label>
                            <div class="control">
                                <textarea class="textarea" id="product-description" placeholder="Describe your product" required></textarea>
                            </div>
                        </div>

                        <div class="columns">
                            <div class="field column is-4">
                                <label class="label">Brand</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="product-brand" required>
                                            <option value="">Select brand</option>
                                            <option value="Ferrari">Ferrari</option>
                                            <option value="Opel">Opel</option>
                                            <option value="Lenovo">Lenovo</option>
                                            <option value="Fossil">Fossil</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="field column is-4">
                                <label class="label">Condition</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="product-condition" required>
                                            <option value="">Select condition</option>
                                            <option value="New">New</option>
                                            <option value="Excellent">Excellent</option>
                                            <option value="Mint">Mint</option>
                                            <option value="Used">Used</option>
                                            <option value="Fair">Fair</option>
                                            <option value="Poor">Poor</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="field column is-4">
                                <label class="label">Finish</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="product-finish" required>
                                            <option value="">Select finish</option>
                                            <option value="Black">Black</option>
                                            <option value="White">White</option>
                                            <option value="Navy">Navy</option>
                                            <option value="Blue">Blue</option>
                                            <option value="Red">Red</option>
                                            <option value="Clear">Clear</option>
                                            <option value="Satin">Satin</option>
                                            <option value="Yellow">Yellow</option>
                                            <option value="Seafoam">Seafoam</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="field is-grouped">
                            <div class="control">
                                <button type="submit" class="button is-warning">Create Product</button>
                            </div>
                            <div class="control">
                                <button type="button" class="button is-light" onclick="showShop()">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Product Detail Section (Hidden initially) -->
        <div id="product-detail-section" class="container" style="display: none; padding: 2rem;">
            <div class="columns">
                <div class="column is-6">
                    <div class="product-detail-image">
                        <div class="product-image" id="detail-product-image" style="height: 400px; border-radius: 8px;">
                            Product Image
                        </div>
                    </div>
                </div>
                <div class="column is-6">
                    <div class="product-detail-info">
                        <h1 class="title is-2" id="detail-product-title">Product Title</h1>
                        <p class="subtitle is-4 has-text-grey">by <span id="detail-product-seller">Seller Name</span></p>

                        <div class="tags" style="margin-bottom: 1rem;">
                            <span class="tag is-info" id="detail-product-condition">Condition</span>
                            <span class="tag is-light" id="detail-product-brand">Brand</span>
                        </div>

                        <div class="content">
                            <h3 class="title is-4">Description</h3>
                            <p id="detail-product-description">Product description will appear here...</p>
                        </div>

                        <div class="columns" style="margin-top: 2rem;">
                            <div class="column">
                                <p><strong>Model:</strong> <span id="detail-product-model">Model</span></p>
                                <p><strong>Finish:</strong> <span id="detail-product-finish">Finish</span></p>
                            </div>
                        </div>

                        <div class="price-section" style="margin: 2rem 0;">
                            <h2 class="title is-3 has-text-primary">$<span id="detail-product-price">0</span></h2>
                        </div>

                        <div class="buttons">
                            <button class="button is-primary is-large" onclick="addToCartFromDetail()">
                                <span class="icon">
                                    <i class="fas fa-cart-plus"></i>
                                </span>
                                <span>Add to Cart</span>
                            </button>

                            <div id="detail-product-actions" style="display: none;">
                                <button class="button is-light" onclick="editProductFromDetail()">
                                    <span class="icon">
                                        <i class="fas fa-edit"></i>
                                    </span>
                                    <span>Edit</span>
                                </button>
                                <button class="button is-danger" onclick="deleteProductFromDetail()">
                                    <span class="icon">
                                        <i class="fas fa-trash"></i>
                                    </span>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="buttons" style="margin-top: 1rem;">
                            <button class="button is-light" onclick="showShop()">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>Back to Shop</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Product Section (Hidden initially) -->
        <div id="edit-product-section" class="container" style="display: none; padding: 2rem;">
            <h1 class="title">Edit Product</h1>
            <div class="columns">
                <div class="column is-8">
                    <form id="edit-product-form">
                        <input type="hidden" id="edit-product-id">

                        <div class="columns">
                            <div class="field column is-9">
                                <label class="label">Title</label>
                                <div class="control">
                                    <input class="input" type="text" id="edit-product-title" placeholder="Product title" required>
                                </div>
                            </div>
                            <div class="field column">
                                <label class="label">Price</label>
                                <div class="control">
                                    <input class="input" type="number" id="edit-product-price" placeholder="0.00" step="0.01" required>
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Model</label>
                            <div class="control">
                                <input class="input" type="text" id="edit-product-model" placeholder="Product model" required>
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Description</label>
                            <div class="control">
                                <textarea class="textarea" id="edit-product-description" placeholder="Describe your product" required></textarea>
                            </div>
                        </div>

                        <div class="columns">
                            <div class="field column is-4">
                                <label class="label">Brand</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="edit-product-brand" required>
                                            <option value="">Select brand</option>
                                            <option value="Ferrari">Ferrari</option>
                                            <option value="Opel">Opel</option>
                                            <option value="Lenovo">Lenovo</option>
                                            <option value="Fossil">Fossil</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="field column is-4">
                                <label class="label">Condition</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="edit-product-condition" required>
                                            <option value="">Select condition</option>
                                            <option value="New">New</option>
                                            <option value="Excellent">Excellent</option>
                                            <option value="Mint">Mint</option>
                                            <option value="Used">Used</option>
                                            <option value="Fair">Fair</option>
                                            <option value="Poor">Poor</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="field column is-4">
                                <label class="label">Finish</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="edit-product-finish" required>
                                            <option value="">Select finish</option>
                                            <option value="Black">Black</option>
                                            <option value="White">White</option>
                                            <option value="Navy">Navy</option>
                                            <option value="Blue">Blue</option>
                                            <option value="Red">Red</option>
                                            <option value="Clear">Clear</option>
                                            <option value="Satin">Satin</option>
                                            <option value="Yellow">Yellow</option>
                                            <option value="Seafoam">Seafoam</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="field is-grouped">
                            <div class="control">
                                <button type="submit" class="button is-success">Update Product</button>
                            </div>
                            <div class="control">
                                <button type="button" class="button is-light" onclick="cancelEditProduct()">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="column is-4">
                    <div class="box">
                        <h2 class="subtitle">Product Preview</h2>
                        <div id="edit-product-preview">
                            <div class="product-thumb">
                                <div class="product-image" id="edit-preview-image">
                                    Product Preview
                                </div>
                            </div>
                            <div class="pa3">
                                <h3 class="title is-5" id="edit-preview-title">Product Title</h3>
                                <p class="has-text-grey">Sold by: <span id="edit-preview-seller">Demo User</span></p>
                                <p class="title is-4 has-text-right">$<span id="edit-preview-price">0</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Section (Hidden initially) -->
        <div id="checkout-section" class="container" style="display: none; padding: 2rem;">
            <h1 class="title">Checkout</h1>
            <div class="columns">
                <div class="column is-8">
                    <div class="box">
                        <h2 class="subtitle">Shipping Information</h2>
                        <form id="checkout-form">
                            <div class="columns">
                                <div class="field column is-6">
                                    <label class="label">First Name</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="First name" required>
                                    </div>
                                </div>
                                <div class="field column is-6">
                                    <label class="label">Last Name</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="Last name" required>
                                    </div>
                                </div>
                            </div>

                            <div class="field">
                                <label class="label">Address</label>
                                <div class="control">
                                    <input class="input" type="text" placeholder="Street address" required>
                                </div>
                            </div>

                            <div class="columns">
                                <div class="field column is-6">
                                    <label class="label">City</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="City" required>
                                    </div>
                                </div>
                                <div class="field column is-3">
                                    <label class="label">State</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="State" required>
                                    </div>
                                </div>
                                <div class="field column is-3">
                                    <label class="label">ZIP Code</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="ZIP" required>
                                    </div>
                                </div>
                            </div>

                            <h2 class="subtitle" style="margin-top: 2rem;">Payment Information</h2>

                            <div class="field">
                                <label class="label">Card Number</label>
                                <div class="control">
                                    <input class="input" type="text" placeholder="1234 5678 9012 3456" maxlength="19" required>
                                </div>
                            </div>

                            <div class="columns">
                                <div class="field column is-6">
                                    <label class="label">Expiry Date</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="MM/YY" maxlength="5" required>
                                    </div>
                                </div>
                                <div class="field column is-6">
                                    <label class="label">CVV</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="123" maxlength="4" required>
                                    </div>
                                </div>
                            </div>

                            <div class="field is-grouped">
                                <div class="control">
                                    <button type="submit" class="button is-success is-large">
                                        <span class="icon">
                                            <i class="fas fa-credit-card"></i>
                                        </span>
                                        <span>Complete Order</span>
                                    </button>
                                </div>
                                <div class="control">
                                    <button type="button" class="button is-light" onclick="showCart()">Back to Cart</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="column is-4">
                    <div class="box">
                        <h2 class="subtitle">Order Summary</h2>
                        <div id="checkout-items"></div>
                        <hr>
                        <div class="level">
                            <div class="level-left">
                                <div class="level-item">
                                    <strong>Subtotal:</strong>
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    <strong>$<span id="checkout-subtotal">0.00</span></strong>
                                </div>
                            </div>
                        </div>
                        <div class="level">
                            <div class="level-left">
                                <div class="level-item">
                                    Shipping:
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    $<span id="checkout-shipping">9.99</span>
                                </div>
                            </div>
                        </div>
                        <div class="level">
                            <div class="level-left">
                                <div class="level-item">
                                    Tax:
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    $<span id="checkout-tax">0.00</span>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="level">
                            <div class="level-left">
                                <div class="level-item">
                                    <strong>Total:</strong>
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    <strong class="has-text-primary">$<span id="checkout-total">0.00</span></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Cart data
        let cart = [];
        let currentUser = null;

        // Sample products
        const products = [
            { id: 1, title: "Ferrari F40", brand: "Ferrari", model: "F40", price: 50000, condition: "Excellent", seller: "John Doe", imageClass: "ferrari", canEdit: true },
            { id: 2, title: "MacBook Pro", brand: "Lenovo", model: "ThinkPad", price: 1200, condition: "New", seller: "Jane Smith", imageClass: "macbook", canEdit: false },
            { id: 3, title: "Vintage Watch", brand: "Fossil", model: "Classic", price: 250, condition: "Used", seller: "Bob Wilson", imageClass: "watch", canEdit: false },
            { id: 4, title: "Gaming Chair", brand: "Opel", model: "Pro", price: 399, condition: "New", seller: "Demo User", imageClass: "chair", canEdit: true },
            { id: 5, title: "Smartphone", brand: "Lenovo", model: "Z6", price: 699, condition: "Excellent", seller: "Alice Brown", imageClass: "phone", canEdit: false },
            { id: 6, title: "Headphones", brand: "Fossil", model: "Audio", price: 149, condition: "Good", seller: "Charlie Davis", imageClass: "headphones", canEdit: false }
        ];

        function login() {
            currentUser = "Demo User";
            document.getElementById('login-screen').style.display = 'none';
            document.getElementById('main-app').style.display = 'block';
            showShop();
            showFlash('Welcome! You are now logged in.', 'success');
        }

        function logout() {
            currentUser = null;
            cart = [];
            updateCartDisplay();
            document.getElementById('main-app').style.display = 'none';
            document.getElementById('login-screen').style.display = 'flex';
            showFlash('You have been logged out.', 'info');
        }

        function hideAllSections() {
            document.getElementById('hero-section').style.display = 'none';
            document.getElementById('products-section').style.display = 'none';
            document.getElementById('cart-section').style.display = 'none';
            document.getElementById('sell-section').style.display = 'none';
            document.getElementById('profile-section').style.display = 'none';
            document.getElementById('product-detail-section').style.display = 'none';
            document.getElementById('edit-product-section').style.display = 'none';
            document.getElementById('checkout-section').style.display = 'none';
        }

        function showShop() {
            hideAllSections();
            document.getElementById('hero-section').style.display = 'block';
            document.getElementById('products-section').style.display = 'grid';
            loadProducts();
        }

        function showCart() {
            hideAllSections();
            document.getElementById('cart-section').style.display = 'block';
            displayCart();
        }

        function showSell() {
            hideAllSections();
            document.getElementById('sell-section').style.display = 'block';
        }

        function showProfile() {
            hideAllSections();
            document.getElementById('profile-section').style.display = 'block';
            loadProfile();
        }

        function showCheckout() {
            if (cart.length === 0) {
                showFlash('Your cart is empty. Add some items before checkout.', 'warning');
                return;
            }
            hideAllSections();
            document.getElementById('checkout-section').style.display = 'block';
            loadCheckout();
        }

        function showProductDetail(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) {
                showFlash('Product not found.', 'danger');
                return;
            }

            hideAllSections();
            document.getElementById('product-detail-section').style.display = 'block';

            // Store current product for detail actions
            window.currentDetailProduct = product;

            // Populate product details
            document.getElementById('detail-product-title').textContent = product.title;
            document.getElementById('detail-product-seller').textContent = product.seller;
            document.getElementById('detail-product-condition').textContent = product.condition;
            document.getElementById('detail-product-brand').textContent = product.brand;
            document.getElementById('detail-product-model').textContent = product.model;
            document.getElementById('detail-product-finish').textContent = product.finish || 'Not specified';
            document.getElementById('detail-product-price').textContent = product.price.toLocaleString();
            document.getElementById('detail-product-description').textContent = product.description || 'No description available.';

            // Set product image
            const imageElement = document.getElementById('detail-product-image');
            imageElement.className = `product-image ${product.imageClass}`;
            imageElement.textContent = product.title;

            // Show/hide edit actions based on ownership
            const actionsElement = document.getElementById('detail-product-actions');
            if (product.canEdit) {
                actionsElement.style.display = 'block';
            } else {
                actionsElement.style.display = 'none';
            }
        }

        function addToCartFromDetail() {
            if (window.currentDetailProduct) {
                addToCart(window.currentDetailProduct.id);
            }
        }

        function editProductFromDetail() {
            if (window.currentDetailProduct && window.currentDetailProduct.canEdit) {
                openEditProductForm(window.currentDetailProduct);
            } else {
                showFlash('You can only edit your own products.', 'danger');
            }
        }

        function deleteProductFromDetail() {
            if (window.currentDetailProduct && window.currentDetailProduct.canEdit) {
                if (confirm(`Are you sure you want to delete "${window.currentDetailProduct.title}"?`)) {
                    const index = products.findIndex(p => p.id === window.currentDetailProduct.id);
                    products.splice(index, 1);
                    showFlash(`Product "${window.currentDetailProduct.title}" was successfully deleted.`, 'success');
                    showShop();
                }
            } else {
                showFlash('You can only delete your own products.', 'danger');
            }
        }

        function loadProducts() {
            const container = document.getElementById('products-section');
            container.innerHTML = products.map(product => `
                <div class="product">
                    <div class="product-thumb" onclick="showProductDetail(${product.id})" style="cursor: pointer;">
                        <div class="product-image ${product.imageClass}">
                            ${product.title}
                        </div>
                        <div class="condition">
                            <span class="tag is-dark">${product.condition}</span>
                        </div>
                    </div>
                    <div class="pa3">
                        <h3 class="title is-5" onclick="showProductDetail(${product.id})" style="cursor: pointer;">${product.title}</h3>
                        <p class="has-text-grey">Sold by: ${product.seller}</p>
                        <p class="title is-4 has-text-right">$${product.price.toLocaleString()}</p>
                        <div class="buttons">
                            <button class="button is-primary is-small" onclick="addToCart(${product.id})">
                                <span class="icon"><i class="fas fa-cart-plus"></i></span>
                                <span>Add to Cart</span>
                            </button>
                            <button class="button is-light is-small" onclick="showProductDetail(${product.id})">
                                <span class="icon"><i class="fas fa-eye"></i></span>
                                <span>View</span>
                            </button>
                            ${product.canEdit ? `
                                <button class="button is-small" onclick="editProduct(${product.id})">Edit</button>
                                <button class="button is-small is-danger" onclick="deleteProduct(${product.id})">Delete</button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.product.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
                showFlash(`Increased quantity of "${product.title}" to ${existingItem.quantity}`, 'success');
            } else {
                cart.push({ product: product, quantity: 1 });
                showFlash(`Added "${product.title}" to your cart`, 'success');
            }
            
            updateCartDisplay();
            animateCartIcon();
        }

        function removeFromCart(productId) {
            const itemIndex = cart.findIndex(item => item.product.id === productId);
            if (itemIndex > -1) {
                const product = cart[itemIndex].product;
                cart.splice(itemIndex, 1);
                showFlash(`Removed "${product.title}" from your cart`, 'info');
                updateCartDisplay();
                displayCart();
            }
        }

        function updateQuantity(productId, newQuantity) {
            const item = cart.find(item => item.product.id === productId);
            if (item) {
                if (newQuantity > 0) {
                    item.quantity = newQuantity;
                    showFlash('Quantity updated', 'success');
                } else {
                    removeFromCart(productId);
                    return;
                }
                updateCartDisplay();
                displayCart();
            }
        }

        function emptyCart() {
            if (cart.length > 0 && confirm('Are you sure you want to empty your cart?')) {
                cart = [];
                updateCartDisplay();
                showFlash('Cart emptied successfully', 'info');
                showShop();
            }
        }

        function updateCartDisplay() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cart-count').textContent = totalItems;
            
            const totalPrice = cart.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
            document.getElementById('total-price').textContent = totalPrice.toLocaleString();
            document.getElementById('total-items').textContent = totalItems;
        }

        function displayCart() {
            const container = document.getElementById('cart-items');
            
            if (cart.length === 0) {
                container.innerHTML = `
                    <div class="box has-text-centered">
                        <h2 class="title is-4">Your cart is empty</h2>
                        <p class="subtitle">Start shopping to add items to your cart!</p>
                        <button class="button is-primary" onclick="showShop()">Start Shopping</button>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="box">
                    ${cart.map(item => `
                        <div class="columns is-vcentered" style="border-bottom: 1px solid #eee; padding: 1rem 0;">
                            <div class="column is-2">
                                <div class="image is-64x64 ${item.product.imageClass}" style="border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold;">
                                    ${item.product.title.substring(0, 8)}
                                </div>
                            </div>
                            <div class="column is-4">
                                <h4 class="title is-5">${item.product.title}</h4>
                                <p class="subtitle is-6">${item.product.brand} - ${item.product.model}</p>
                            </div>
                            <div class="column is-2">
                                <div class="field has-addons">
                                    <div class="control">
                                        <input class="input is-small" type="number" value="${item.quantity}" min="1" max="99" 
                                               onchange="updateQuantity(${item.product.id}, this.value)" style="width: 60px;">
                                    </div>
                                    <div class="control">
                                        <button class="button is-small is-info" onclick="updateQuantity(${item.product.id}, document.querySelector('input[onchange*=\\'${item.product.id}\\']').value)">Update</button>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <span class="has-text-weight-bold">$${item.product.price.toLocaleString()}</span>
                            </div>
                            <div class="column is-2">
                                <span class="has-text-weight-bold">$${(item.product.price * item.quantity).toLocaleString()}</span>
                            </div>
                            <div class="column is-1">
                                <button class="button is-small is-danger" onclick="removeFromCart(${item.product.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function showFlash(message, type) {
            const container = document.getElementById('flash-container');
            const className = type === 'success' ? 'is-success' : type === 'info' ? 'is-info' : 'is-danger';
            
            const flash = document.createElement('div');
            flash.className = `notification ${className} fade-out`;
            flash.innerHTML = `
                <button class="delete" onclick="this.parentElement.remove()"></button>
                ${message}
            `;
            
            container.appendChild(flash);
            
            setTimeout(() => {
                if (flash.parentElement) {
                    flash.remove();
                }
            }, 3000);
        }

        function animateCartIcon() {
            const cartIcon = document.getElementById('cart-count');
            cartIcon.classList.add('cart-badge');
            setTimeout(() => {
                cartIcon.classList.remove('cart-badge');
            }, 500);
        }

        function showNewProduct() {
            showSell();
        }

        function deleteProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (product && product.canEdit && confirm(`Are you sure you want to delete "${product.title}"?`)) {
                const index = products.findIndex(p => p.id === productId);
                products.splice(index, 1);
                showFlash(`Product "${product.title}" was successfully deleted.`, 'success');

                // Refresh shop view if visible
                if (document.getElementById('products-section').style.display === 'grid') {
                    loadProducts();
                }

                // Refresh profile view if visible
                if (document.getElementById('profile-section').style.display === 'block') {
                    loadProfile();
                }
            } else if (product && !product.canEdit) {
                showFlash('You can only delete your own products.', 'danger');
            }
        }

        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (product && product.canEdit) {
                openEditProductForm(product);
            } else {
                showFlash('You can only edit your own products.', 'danger');
            }
        }

        function loadProfile() {
            // Update statistics
            const myProducts = products.filter(p => p.canEdit);
            document.getElementById('products-count').textContent = myProducts.length;
            document.getElementById('total-sales').textContent = '0'; // Demo value
            document.getElementById('profile-cart-count').textContent = cart.reduce((sum, item) => sum + item.quantity, 0);

            // Load my products
            const myProductsList = document.getElementById('my-products-list');
            if (myProducts.length === 0) {
                myProductsList.innerHTML = '<p class="has-text-grey">You haven\'t listed any products yet. <a href="#" onclick="showSell()">Create your first listing!</a></p>';
            } else {
                myProductsList.innerHTML = myProducts.map(product => `
                    <div class="box">
                        <div class="columns is-vcentered">
                            <div class="column is-2">
                                <div class="image is-64x64 ${product.imageClass}" style="border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem; font-weight: bold;">
                                    ${product.title.substring(0, 8)}
                                </div>
                            </div>
                            <div class="column">
                                <h4 class="title is-6">${product.title}</h4>
                                <p class="subtitle is-7">${product.brand} - ${product.model}</p>
                            </div>
                            <div class="column is-narrow">
                                <span class="tag is-info">${product.condition}</span>
                            </div>
                            <div class="column is-narrow">
                                <strong>$${product.price.toLocaleString()}</strong>
                            </div>
                            <div class="column is-narrow">
                                <div class="buttons">
                                    <button class="button is-small" onclick="editProductFromProfile(${product.id})">Edit</button>
                                    <button class="button is-small is-danger" onclick="deleteProductFromProfile(${product.id})">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        function toggleEditProfile() {
            const nameField = document.getElementById('profile-name');
            const emailField = document.getElementById('profile-email');
            const editButton = document.getElementById('edit-profile-text');

            if (nameField.readOnly) {
                // Enable editing
                nameField.readOnly = false;
                emailField.readOnly = false;
                nameField.classList.add('is-focused');
                emailField.classList.add('is-focused');
                editButton.textContent = 'Save Changes';
                showFlash('You can now edit your profile information', 'info');
            } else {
                // Save changes
                nameField.readOnly = true;
                emailField.readOnly = true;
                nameField.classList.remove('is-focused');
                emailField.classList.remove('is-focused');
                editButton.textContent = 'Edit Profile';

                // Update current user name
                currentUser = nameField.value;

                showFlash('Profile updated successfully!', 'success');
            }
        }

        function editProductFromProfile(productId) {
            const product = products.find(p => p.id === productId);
            if (product && product.canEdit) {
                openEditProductForm(product);
            } else {
                showFlash('You can only edit your own products.', 'danger');
            }
        }

        function deleteProductFromProfile(productId) {
            const product = products.find(p => p.id === productId);
            if (product && product.canEdit && confirm(`Are you sure you want to delete "${product.title}"?`)) {
                const index = products.findIndex(p => p.id === productId);
                products.splice(index, 1);
                showFlash(`Product "${product.title}" was successfully deleted.`, 'success');

                // Refresh profile to update the list
                loadProfile();

                // If we're also viewing the shop, refresh it too
                if (document.getElementById('products-section').style.display === 'grid') {
                    loadProducts();
                }
            } else if (product && !product.canEdit) {
                showFlash('You can only delete your own products.', 'danger');
            }
        }

        function loadCheckout() {
            // Update checkout summary
            const subtotal = cart.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
            const shipping = cart.length > 0 ? 9.99 : 0;
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + shipping + tax;

            document.getElementById('checkout-subtotal').textContent = subtotal.toLocaleString();
            document.getElementById('checkout-shipping').textContent = shipping.toFixed(2);
            document.getElementById('checkout-tax').textContent = tax.toFixed(2);
            document.getElementById('checkout-total').textContent = total.toFixed(2);

            // Load checkout items
            const checkoutItems = document.getElementById('checkout-items');
            checkoutItems.innerHTML = cart.map(item => `
                <div class="level" style="margin-bottom: 0.5rem;">
                    <div class="level-left">
                        <div class="level-item">
                            <small>${item.product.title} x${item.quantity}</small>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <small>$${(item.product.price * item.quantity).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function createProduct(event) {
            event.preventDefault();

            const title = document.getElementById('product-title').value;
            const price = parseFloat(document.getElementById('product-price').value);
            const model = document.getElementById('product-model').value;
            const description = document.getElementById('product-description').value;
            const brand = document.getElementById('product-brand').value;
            const condition = document.getElementById('product-condition').value;
            const finish = document.getElementById('product-finish').value;

            if (!title || !price || !model || !description || !brand || !condition || !finish) {
                showFlash('Please fill in all fields', 'danger');
                return;
            }

            const imageClasses = ['ferrari', 'macbook', 'watch', 'chair', 'phone', 'headphones'];
            const randomImageClass = imageClasses[Math.floor(Math.random() * imageClasses.length)];

            const newProduct = {
                id: products.length + 1,
                title: title,
                brand: brand,
                model: model,
                price: price,
                condition: condition,
                seller: currentUser,
                imageClass: randomImageClass,
                canEdit: true
            };

            products.push(newProduct);

            // Clear form
            document.getElementById('product-form').reset();

            showFlash(`Product "${title}" was successfully created. Your product is now live!`, 'success');
            showShop();
        }

        function toggleNavbar() {
            const navbarMenu = document.getElementById('navbar-menu');
            navbarMenu.classList.toggle('is-active');
        }

        function toggleAccountDropdown() {
            const dropdown = document.querySelector('.navbar-item.has-dropdown');
            dropdown.classList.toggle('is-active');
        }

        function openEditProductForm(product) {
            hideAllSections();
            document.getElementById('edit-product-section').style.display = 'block';

            // Populate form with product data
            document.getElementById('edit-product-id').value = product.id;
            document.getElementById('edit-product-title').value = product.title;
            document.getElementById('edit-product-price').value = product.price;
            document.getElementById('edit-product-model').value = product.model;
            document.getElementById('edit-product-description').value = product.description || '';
            document.getElementById('edit-product-brand').value = product.brand;
            document.getElementById('edit-product-condition').value = product.condition;
            document.getElementById('edit-product-finish').value = product.finish || '';

            // Update preview
            updateEditPreview();

            showFlash(`Editing "${product.title}"`, 'info');
        }

        function updateEditPreview() {
            const title = document.getElementById('edit-product-title').value || 'Product Title';
            const price = document.getElementById('edit-product-price').value || '0';
            const imageClass = products.find(p => p.id == document.getElementById('edit-product-id').value)?.imageClass || 'ferrari';

            document.getElementById('edit-preview-title').textContent = title;
            document.getElementById('edit-preview-price').textContent = parseFloat(price).toLocaleString();
            document.getElementById('edit-preview-image').className = `product-image ${imageClass}`;
            document.getElementById('edit-preview-image').textContent = title;
        }

        function cancelEditProduct() {
            // Return to previous view (profile or product detail)
            if (window.currentDetailProduct) {
                showProductDetail(window.currentDetailProduct.id);
            } else {
                showProfile();
            }
        }

        function updateProduct(event) {
            event.preventDefault();

            const productId = parseInt(document.getElementById('edit-product-id').value);
            const product = products.find(p => p.id === productId);

            if (!product || !product.canEdit) {
                showFlash('Product not found or you do not have permission to edit it.', 'danger');
                return;
            }

            // Update product data
            product.title = document.getElementById('edit-product-title').value;
            product.price = parseFloat(document.getElementById('edit-product-price').value);
            product.model = document.getElementById('edit-product-model').value;
            product.description = document.getElementById('edit-product-description').value;
            product.brand = document.getElementById('edit-product-brand').value;
            product.condition = document.getElementById('edit-product-condition').value;
            product.finish = document.getElementById('edit-product-finish').value;

            showFlash(`Product "${product.title}" updated successfully!`, 'success');

            // Update the current detail product reference
            window.currentDetailProduct = product;

            // Go back to detail view or profile
            if (window.currentDetailProduct) {
                showProductDetail(product.id);
            } else {
                showProfile();
            }
        }

        function completeOrder(event) {
            event.preventDefault();

            // Simulate order processing
            showFlash('Processing your order...', 'info');

            setTimeout(() => {
                const orderNumber = Math.floor(Math.random() * 1000000);
                showFlash(`Order #${orderNumber} completed successfully! Thank you for your purchase.`, 'success');

                // Clear cart
                cart = [];
                updateCartDisplay();

                // Clear form
                document.getElementById('checkout-form').reset();

                // Go back to shop
                setTimeout(() => {
                    showShop();
                }, 2000);
            }, 1500);
        }

        // Initialize
        updateCartDisplay();

        // Add form submission handlers
        document.getElementById('product-form').addEventListener('submit', createProduct);
        document.getElementById('edit-product-form').addEventListener('submit', updateProduct);
        document.getElementById('checkout-form').addEventListener('submit', completeOrder);

        // Add real-time preview for edit form
        ['edit-product-title', 'edit-product-price'].forEach(id => {
            document.getElementById(id).addEventListener('input', updateEditPreview);
        });

        // Add click outside handler for dropdown
        document.addEventListener('click', function(event) {
            const dropdown = document.querySelector('.navbar-item.has-dropdown');
            if (dropdown && !dropdown.contains(event.target)) {
                dropdown.classList.remove('is-active');
            }
        });
    </script>
</body>
</html>
