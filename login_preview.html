<!DOCTYPE html>
<html>
<head>
    <title>Shop - Login</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container { min-height: 100vh; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .login-box { background: white; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); padding: 3rem; max-width: 400px; width: 100%; }
        .brand-title { text-align: center; margin-bottom: 2rem; color: #363636; }
        .field { margin-bottom: 1.5rem; }
        .button.is-fullwidth { margin-top: 1rem; }
        .divider { text-align: center; margin: 2rem 0; color: #999; position: relative; }
        .divider::before { content: ''; position: absolute; top: 50%; left: 0; right: 0; height: 1px; background: #ddd; }
        .divider span { background: white; padding: 0 1rem; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="brand-title">
                <h1 class="title is-3">
                    <span class="icon-text">
                        <span class="icon has-text-primary">
                            <i class="fas fa-shopping-bag"></i>
                        </span>
                        <span>Shop</span>
                    </span>
                </h1>
                <p class="subtitle is-6">Welcome to our e-commerce platform</p>
            </div>

            <!-- Login Form -->
            <div id="login-form">
                <form>
                    <div class="field">
                        <label class="label">Email</label>
                        <div class="control has-icons-left">
                            <input class="input" type="email" placeholder="Enter your email" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-envelope"></i>
                            </span>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Password</label>
                        <div class="control has-icons-left">
                            <input class="input" type="password" placeholder="Enter your password" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-lock"></i>
                            </span>
                        </div>
                    </div>

                    <div class="field">
                        <label class="checkbox">
                            <input type="checkbox">
                            Remember me
                        </label>
                    </div>

                    <button type="button" class="button is-primary is-fullwidth" onclick="showShop()">
                        <span class="icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </span>
                        <span>Sign In</span>
                    </button>
                </form>

                <div class="divider">
                    <span>or</span>
                </div>

                <button type="button" class="button is-light is-fullwidth" onclick="showRegister()">
                    <span class="icon">
                        <i class="fas fa-user-plus"></i>
                    </span>
                    <span>Create New Account</span>
                </button>

                <div class="has-text-centered" style="margin-top: 1rem;">
                    <a href="#" class="has-text-grey">Forgot your password?</a>
                </div>
            </div>

            <!-- Register Form (Hidden by default) -->
            <div id="register-form" style="display: none;">
                <form>
                    <div class="field">
                        <label class="label">Full Name</label>
                        <div class="control has-icons-left">
                            <input class="input" type="text" placeholder="Enter your full name" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-user"></i>
                            </span>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Email</label>
                        <div class="control has-icons-left">
                            <input class="input" type="email" placeholder="Enter your email" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-envelope"></i>
                            </span>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Password</label>
                        <div class="control has-icons-left">
                            <input class="input" type="password" placeholder="Create a password" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-lock"></i>
                            </span>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Confirm Password</label>
                        <div class="control has-icons-left">
                            <input class="input" type="password" placeholder="Confirm your password" required>
                            <span class="icon is-small is-left">
                                <i class="fas fa-lock"></i>
                            </span>
                        </div>
                    </div>

                    <button type="button" class="button is-success is-fullwidth" onclick="showShop()">
                        <span class="icon">
                            <i class="fas fa-user-plus"></i>
                        </span>
                        <span>Create Account</span>
                    </button>
                </form>

                <div class="divider">
                    <span>or</span>
                </div>

                <button type="button" class="button is-light is-fullwidth" onclick="showLogin()">
                    <span class="icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </span>
                    <span>Already have an account? Sign In</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showLogin() {
            document.getElementById('login-form').style.display = 'block';
            document.getElementById('register-form').style.display = 'none';
        }

        function showRegister() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('register-form').style.display = 'block';
        }

        function showShop() {
            // Simulate successful login/register
            window.location.href = 'frontend_preview.html';
        }
    </script>
</body>
</html>
